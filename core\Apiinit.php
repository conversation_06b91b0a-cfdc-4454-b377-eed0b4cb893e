<?php

namespace modules\facebookleadsintegration\core;

class Apiinit
{
    public static function the_da_vinci_code($module_name)
    {
        // License verification removed - always return true
        return true;
    }

    
    public static function ease_of_mind($module_name)
    {
        // Function existence check removed - no license verification needed
        return true;
    }

    
    public static function activate($module)
    {
        // Activation screen removed - module activates without license verification
        return true;
    }



    
    public static function pre_validate($module_name, $code = '')
    {
        // License validation removed - always return success
        return ['status' => true];
    }
}
