{"name": "rmccue/requests", "description": "A HTTP library written in PHP, for human beings.", "homepage": "https://requests.ryanmccue.info/", "license": "ISC", "type": "library", "keywords": ["http", "idna", "iri", "ipv6", "curl", "sockets", "fsockopen"], "authors": [{"name": "<PERSON>", "homepage": "https://rmccue.io/"}, {"name": "<PERSON>", "homepage": "https://github.com/schlessera"}, {"name": "<PERSON>", "homepage": "https://github.com/jrfnl"}, {"name": "Contributors", "homepage": "https://github.com/WordPress/Requests/graphs/contributors"}], "support": {"issues": "https://github.com/WordPress/Requests/issues", "source": "https://github.com/WordPress/Requests", "docs": "https://requests.ryanmccue.info/"}, "require": {"php": ">=5.6", "ext-json": "*"}, "config": {"allow-plugins": {"dealerdirect/phpcodesniffer-composer-installer": true}}, "require-dev": {"requests/test-server": "dev-main", "squizlabs/php_codesniffer": "^3.6", "phpcompatibility/php-compatibility": "^9.0", "wp-coding-standards/wpcs": "^2.0", "dealerdirect/phpcodesniffer-composer-installer": "^0.7", "php-parallel-lint/php-parallel-lint": "^1.3.1", "php-parallel-lint/php-console-highlighter": "^0.5.0", "yoast/phpunit-polyfills": "^1.0.0", "roave/security-advisories": "dev-latest"}, "autoload": {"psr-4": {"WpOrg\\Requests\\": "src/"}, "classmap": ["library/Requests.php"], "files": ["library/Deprecated.php"]}, "autoload-dev": {"psr-4": {"WpOrg\\Requests\\Tests\\": "tests/"}}, "scripts": {"lint": ["@php ./vendor/php-parallel-lint/php-parallel-lint/parallel-lint . -e php --exclude vendor --exclude .git"], "checkcs": ["@php ./vendor/squizlabs/php_codesniffer/bin/phpcs"], "fixcs": ["@php ./vendor/squizlabs/php_codesniffer/bin/phpcbf"], "test": ["@php ./vendor/phpunit/phpunit/phpunit --no-coverage"], "coverage": ["@php ./vendor/phpunit/phpunit/phpunit"]}}